"""
Dynamic Strategy Selector
AI-driven strategy selection based on market conditions, account settings, and risk management
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from enum import Enum

from strategies.base_strategy import MarketData, StrategyType
from money_management.base_strategy import AccountInfo

logger = logging.getLogger(__name__)

class MarketSession(Enum):
    ASIAN = "asian_session"
    LONDON = "london_session"
    NEW_YORK = "new_york_session"
    OVERLAP = "overlap_session"

class MarketCondition(Enum):
    TRENDING = "trending"
    RANGING = "ranging"
    VOLATILE = "volatile"
    QUIET = "quiet"

class DynamicStrategySelector:
    """Selects optimal trading strategy based on current market conditions and account settings"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def select_strategy(
        self,
        account_config: Dict[str, Any],
        market_data: MarketData,
        account_info: AccountInfo,
        current_time: Optional[datetime] = None
    ) -> str:
        """
        Select the optimal strategy based on multiple factors
        
        Args:
            account_config: Account configuration including strategy selection settings
            market_data: Current market data and conditions
            account_info: Account balance, equity, and risk information
            current_time: Current time for session detection
            
        Returns:
            Selected strategy name
        """
        try:
            # Get strategy selection configuration
            strategy_config = account_config.get('strategy_selection', {})
            
            # If dynamic mode is disabled, return default strategy
            if strategy_config.get('mode') != 'dynamic':
                default_strategy = account_config.get('strategy', 'trend_following')
                self.logger.info(f"📋 STATIC STRATEGY MODE: Using hardcoded strategy '{default_strategy}' from accounts.json")
                self.logger.info(f"   ⚠️  AI STRATEGY SELECTION DISABLED: Set mode='dynamic' in strategy_selection config to enable AI-driven selection")
                return default_strategy
            
            available_strategies = strategy_config.get('available_strategies', ['trend_following'])
            selection_criteria = strategy_config.get('selection_criteria', {})
            
            # Analyze current market conditions
            market_condition = self._analyze_market_condition(market_data, selection_criteria)
            market_session = self._get_current_market_session(current_time)
            
            # Calculate strategy scores
            strategy_scores = {}
            for strategy in available_strategies:
                score = self._calculate_strategy_score(
                    strategy, market_condition, market_session, 
                    account_info, selection_criteria
                )
                strategy_scores[strategy] = score
            
            # Select strategy with highest score
            selected_strategy = max(strategy_scores, key=strategy_scores.get)
            highest_score = strategy_scores[selected_strategy]

            # Generate detailed reasoning for the selection
            reasoning_parts = []

            # Market condition reasoning
            if market_condition == MarketCondition.TRENDING:
                reasoning_parts.append("Strong trend detected - favoring trend-following strategies")
            elif market_condition == MarketCondition.VOLATILE:
                reasoning_parts.append("High volatility detected - favoring breakout strategies")
            elif market_condition == MarketCondition.RANGING:
                reasoning_parts.append("Ranging market detected - favoring mean reversion strategies")
            else:
                reasoning_parts.append("Quiet market conditions - using conservative approach")

            # Session reasoning
            if market_session == MarketSession.LONDON:
                reasoning_parts.append("London session active - high liquidity expected")
            elif market_session == MarketSession.NEW_YORK:
                reasoning_parts.append("New York session active - strong momentum expected")
            elif market_session == MarketSession.ASIAN:
                reasoning_parts.append("Asian session active - lower volatility expected")
            else:
                reasoning_parts.append("Off-hours trading - reduced activity expected")

            # Risk reasoning
            if account_info.margin_level < 200:
                reasoning_parts.append("Low margin level - prioritizing conservative strategies")
            elif account_info.margin_level > 500:
                reasoning_parts.append("High margin level - allowing more aggressive strategies")

            # Volatility reasoning
            if market_data.volatility > 0.01:
                reasoning_parts.append("High volatility - breakout opportunities available")
            elif market_data.volatility < 0.005:
                reasoning_parts.append("Low volatility - range-bound strategies preferred")

            reasoning = "; ".join(reasoning_parts)

            self.logger.info(f"🎯 AI STRATEGY SELECTION COMPLETE: {selected_strategy} selected (score: {highest_score:.2f})")
            self.logger.info(f"   📊 Market Analysis: {market_condition.value} condition, {market_session.value} session")
            self.logger.info(f"   📈 Market Metrics: Volatility={market_data.volatility:.5f}, Volume={market_data.volume}")
            self.logger.info(f"   💰 Account Risk: Balance=${account_info.balance:.2f}, Margin Level={account_info.margin_level:.1f}%")
            self.logger.info(f"   🧠 AI REASONING: {reasoning}")
            self.logger.info(f"   📊 Strategy Scores: {strategy_scores}")
            self.logger.info(f"   🚀 FINAL DECISION: Using {selected_strategy} strategy based on AI analysis")

            return selected_strategy
            
        except Exception as e:
            fallback_strategy = account_config.get('strategy', 'trend_following')
            self.logger.error(f"❌ AI STRATEGY SELECTION FAILED: {e}")
            self.logger.error(f"   🔄 FALLBACK: Using default strategy '{fallback_strategy}' from accounts.json")
            self.logger.error(f"   ⚠️  IMPACT: Trading will continue with hardcoded strategy instead of AI-optimized selection")
            # Fallback to default strategy
            return fallback_strategy
    
    def _analyze_market_condition(self, market_data: MarketData, criteria: Dict[str, Any]) -> MarketCondition:
        """Analyze current market condition based on volatility and trend strength"""
        try:
            volatility_threshold = criteria.get('market_volatility_threshold', 0.015)
            trend_threshold = criteria.get('trend_strength_threshold', 0.7)
            
            # Determine market condition based on volatility and trend
            if market_data.volatility > volatility_threshold * 1.5:
                return MarketCondition.VOLATILE
            elif market_data.volatility < volatility_threshold * 0.5:
                return MarketCondition.QUIET
            elif abs(market_data.trend_strength) > trend_threshold:
                return MarketCondition.TRENDING
            else:
                return MarketCondition.RANGING
                
        except Exception as e:
            self.logger.error(f"Error analyzing market condition: {e}")
            return MarketCondition.RANGING
    
    def _get_current_market_session(self, current_time: Optional[datetime] = None) -> MarketSession:
        """Determine current market session based on UTC time"""
        try:
            if current_time is None:
                current_time = datetime.now(timezone.utc)
            
            hour = current_time.hour
            
            # Market session times (UTC)
            # Asian: 22:00 - 08:00 UTC
            # London: 08:00 - 16:00 UTC  
            # New York: 13:00 - 21:00 UTC
            
            if 8 <= hour < 13:  # London only
                return MarketSession.LONDON
            elif 13 <= hour < 16:  # London + New York overlap
                return MarketSession.OVERLAP
            elif 16 <= hour < 21:  # New York only
                return MarketSession.NEW_YORK
            else:  # Asian session (21:00 - 08:00 UTC)
                return MarketSession.ASIAN
                
        except Exception as e:
            self.logger.error(f"Error determining market session: {e}")
            return MarketSession.LONDON
    
    def _calculate_strategy_score(
        self,
        strategy: str,
        market_condition: MarketCondition,
        market_session: MarketSession,
        account_info: AccountInfo,
        criteria: Dict[str, Any]
    ) -> float:
        """Calculate score for a strategy based on current conditions"""
        try:
            score = 0.0
            
            # Base score for market condition compatibility
            condition_scores = {
                'trend_following': {
                    MarketCondition.TRENDING: 1.0,
                    MarketCondition.VOLATILE: 0.7,
                    MarketCondition.RANGING: 0.3,
                    MarketCondition.QUIET: 0.4
                },
                'mean_reversion': {
                    MarketCondition.RANGING: 1.0,
                    MarketCondition.QUIET: 0.8,
                    MarketCondition.TRENDING: 0.2,
                    MarketCondition.VOLATILE: 0.5
                },
                'breakout': {
                    MarketCondition.VOLATILE: 1.0,
                    MarketCondition.TRENDING: 0.8,
                    MarketCondition.RANGING: 0.6,
                    MarketCondition.QUIET: 0.1
                }
            }
            
            score += condition_scores.get(strategy, {}).get(market_condition, 0.5) * 40
            
            # Session preference bonus
            session_preferences = criteria.get('market_hours_preference', {})
            preferred_strategy = session_preferences.get(market_session.value)
            if preferred_strategy == strategy:
                score += 20
            
            # Account balance consideration
            balance_thresholds = criteria.get('account_balance_thresholds', {})
            for threshold_name, threshold_config in balance_thresholds.items():
                balance_below = threshold_config.get('balance_below')
                balance_above = threshold_config.get('balance_above')
                preferred_strategy = threshold_config.get('preferred_strategy')
                
                if preferred_strategy == strategy:
                    if balance_below and account_info.balance < balance_below:
                        score += 15
                    elif balance_above and account_info.balance > balance_above:
                        score += 15
            
            # Risk level adjustment
            if account_info.margin_level < 200:  # High risk
                if strategy == 'trend_following':  # More conservative
                    score += 10
            elif account_info.margin_level > 500:  # Low risk
                if strategy in ['breakout', 'mean_reversion']:  # More aggressive
                    score += 5
            
            return score
            
        except Exception as e:
            self.logger.error(f"Error calculating strategy score for {strategy}: {e}")
            return 0.0

# Global instance
dynamic_strategy_selector = DynamicStrategySelector()
