#!/usr/bin/env python3
"""
Verification script to ensure all datetime fixes are working correctly
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add src directory to path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

def test_loss_prevention_with_mixed_data():
    """Test loss prevention with realistic mixed datetime data"""
    print("🔍 Testing Loss Prevention with Mixed DateTime Data...")
    
    try:
        from risk_management.loss_prevention import LossPreventionSystem
        from money_management.base_strategy import AccountInfo
        
        loss_prevention = LossPreventionSystem()
        
        # Create realistic test data with mixed formats (simulating MT5 + legacy data)
        now = datetime.now()
        test_trades = [
            # Recent trades from MT5 (datetime objects)
            {'profit': -25.50, 'time': now - timedelta(hours=1), 'symbol': 'EURUSD'},
            {'profit': -15.75, 'time': now - timedelta(hours=2), 'symbol': 'GBPUSD'},
            {'profit': 30.25, 'time': now - timedelta(hours=3), 'symbol': 'EURUSD'},
            
            # Older trades from legacy system (timestamps)
            {'profit': -45.00, 'time': (now - timedelta(hours=4)).timestamp(), 'symbol': 'USDJPY'},
            {'profit': 20.50, 'time': (now - timedelta(hours=5)).timestamp(), 'symbol': 'EURUSD'},
            {'profit': -35.25, 'time': (now - timedelta(hours=6)).timestamp(), 'symbol': 'GBPUSD'},
        ]
        
        # Test loss streak analysis
        loss_streak = loss_prevention._analyze_loss_streak("test_account", test_trades)
        print(f"   ✅ Loss streak: {loss_streak.consecutive_losses} consecutive losses")
        print(f"   ✅ Total loss amount: ${loss_streak.total_loss_amount:.2f}")
        
        # Test daily loss calculation
        daily_loss = loss_prevention._calculate_daily_loss(test_trades)
        print(f"   ✅ Daily loss: ${daily_loss:.2f}")
        
        # Test full risk assessment
        account_info = AccountInfo(
            balance=1000.0, equity=950.0, margin=100.0, 
            free_margin=850.0, margin_level=950.0, 
            currency="USD", leverage=100
        )
        
        account_settings = {
            'money_management_settings': {
                'max_daily_loss': 200.0,
                'max_consecutive_losses': 5
            }
        }
        
        risk_assessment = loss_prevention.assess_trading_risk(
            "test_account", account_info, test_trades, account_settings
        )
        
        print(f"   ✅ Risk level: {risk_assessment.risk_level}")
        print(f"   ✅ Trading allowed: {risk_assessment.allow_trading}")
        print(f"   ✅ Volume multiplier: {risk_assessment.recommended_volume_multiplier}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sorting_robustness():
    """Test that sorting works with various datetime formats"""
    print("\n🔍 Testing Sorting Robustness...")
    
    try:
        # Create test data with various time formats
        now = datetime.now()
        test_data = [
            {'time': now - timedelta(hours=5), 'profit': -10, 'id': 1},  # datetime
            {'time': (now - timedelta(hours=4)).timestamp(), 'profit': 20, 'id': 2},  # timestamp
            {'time': now - timedelta(hours=3), 'profit': -30, 'id': 3},  # datetime
            {'time': (now - timedelta(hours=2)).timestamp(), 'profit': 40, 'id': 4},  # timestamp
            {'time': now - timedelta(hours=1), 'profit': -50, 'id': 5},  # datetime
            {'time': None, 'profit': 60, 'id': 6},  # invalid time
            {'time': 'invalid', 'profit': -70, 'id': 7},  # invalid time
        ]
        
        # Helper function (same as in loss_prevention.py)
        def get_trade_timestamp(trade):
            time_value = trade.get('time', 0)
            if isinstance(time_value, datetime):
                return time_value.timestamp()
            elif isinstance(time_value, (int, float)):
                return time_value
            else:
                return 0
        
        # Sort by time (most recent first)
        sorted_data = sorted(test_data, key=get_trade_timestamp, reverse=True)
        
        print("   ✅ Sorted data (most recent first):")
        for item in sorted_data:
            time_value = item['time']
            if isinstance(time_value, datetime):
                time_str = time_value.strftime('%H:%M:%S')
            elif isinstance(time_value, (int, float)) and time_value > 0:
                time_str = datetime.fromtimestamp(time_value).strftime('%H:%M:%S')
            else:
                time_str = "INVALID"
            
            print(f"      ID {item['id']}: {time_str} - Profit: ${item['profit']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Sorting test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_age_calculations():
    """Test age calculations with different datetime formats"""
    print("\n🔍 Testing Age Calculations...")
    
    try:
        now = datetime.now()
        
        # Test data with different time formats
        test_cases = [
            {'name': 'DateTime Object', 'time': now - timedelta(hours=2.5)},
            {'name': 'Timestamp Float', 'time': (now - timedelta(hours=1.5)).timestamp()},
            {'name': 'Timestamp Int', 'time': int((now - timedelta(hours=3.5)).timestamp())},
            {'name': 'Invalid Time', 'time': None},
            {'name': 'String Time', 'time': 'invalid'},
        ]
        
        for case in test_cases:
            time_value = case['time']
            
            # Age calculation logic (same as in trade_manager.py)
            if isinstance(time_value, datetime):
                age_hours = (datetime.now() - time_value).total_seconds() / 3600
            elif isinstance(time_value, (int, float)) and time_value > 0:
                age_hours = (datetime.now().timestamp() - time_value) / 3600
            else:
                age_hours = 0
            
            print(f"   ✅ {case['name']}: {age_hours:.1f} hours")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Age calculation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all verification tests"""
    print("🚀 DATETIME FIXES VERIFICATION")
    print("=" * 50)
    
    tests = [
        test_loss_prevention_with_mixed_data,
        test_sorting_robustness,
        test_age_calculations,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 VERIFICATION RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ ALL DATETIME FIXES ARE WORKING CORRECTLY!")
        print("\n🎉 The system can now handle:")
        print("   • Mixed datetime objects and timestamps")
        print("   • Safe sorting operations")
        print("   • Robust age calculations")
        print("   • Error-free risk assessments")
        print("\n💡 The original errors should no longer occur:")
        print("   • 'datetime.datetime' object cannot be interpreted as an integer")
        print("   • Risk management calculation failures")
        print("   • Trading suspension due to datetime errors")
        return True
    else:
        print("❌ Some datetime handling issues may remain")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
