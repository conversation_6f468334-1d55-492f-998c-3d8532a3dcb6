#!/usr/bin/env python3
"""
Unified Risk Management System
Consolidates all risk tracking and management functionality
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from money_management.base_strategy import AccountInfo
from logging_system.logger import get_logger, trading_logger

logger = get_logger(__name__)

class RiskLevel(Enum):
    """Risk level enumeration"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

@dataclass
class DailyStats:
    """Daily trading statistics for an account"""
    account_id: str
    date: str
    trades_count: int
    realized_pnl: float
    unrealized_pnl: float
    total_pnl: float
    consecutive_losses: int
    last_update: datetime

@dataclass
class RiskAssessment:
    """Comprehensive risk assessment result"""
    account_id: str
    risk_level: RiskLevel
    allow_trading: bool
    allow_new_positions: bool
    recommended_volume_multiplier: float
    reasons: List[str]
    cooling_off_until: Optional[datetime] = None
    daily_stats: Optional[DailyStats] = None

@dataclass
class AccountLimits:
    """Account-specific risk limits"""
    max_daily_trades: int
    max_open_positions: int
    max_pending_orders: int
    max_daily_loss: float
    max_daily_loss_percent: Optional[float]
    max_drawdown_percent: float
    max_consecutive_losses: int = 5

class UnifiedRiskManager:
    """
    Unified Risk Management System
    Consolidates all risk tracking and replaces duplicate systems
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Unified tracking dictionaries
        self.daily_stats: Dict[str, DailyStats] = {}  # account_date -> DailyStats
        self.account_limits: Dict[str, AccountLimits] = {}  # account_id -> AccountLimits
        self.risk_assessments: Dict[str, RiskAssessment] = {}  # account_id -> RiskAssessment
        self.cooling_off_accounts: Dict[str, datetime] = {}  # account_id -> cooling_off_until
        
        # Thread safety
        self._lock = asyncio.Lock()
        
        self.logger.info("🛡️ Unified Risk Manager initialized")
    
    async def initialize_account_limits(self, account_config: Dict[str, Any]) -> None:
        """Initialize risk limits for an account from configuration"""
        async with self._lock:
            try:
                account_id = account_config['account_id']
                money_mgmt_config = account_config.get('money_management_config', 
                                                     account_config.get('money_management_settings', {}))
                
                # Create unified account limits
                limits = AccountLimits(
                    max_daily_trades=money_mgmt_config.get('max_daily_trades', 10),
                    max_open_positions=money_mgmt_config.get('max_open_positions', 5),
                    max_pending_orders=money_mgmt_config.get('max_pending_orders', 10),
                    max_daily_loss=money_mgmt_config.get('max_daily_loss', 5.0),
                    max_daily_loss_percent=money_mgmt_config.get('max_daily_loss_percent'),
                    max_drawdown_percent=money_mgmt_config.get('max_drawdown_percent', 15.0),
                    max_consecutive_losses=money_mgmt_config.get('max_consecutive_losses', 5)
                )
                
                self.account_limits[account_id] = limits
                
                self.logger.info(f"✅ Risk limits initialized for account {account_id}")
                self.logger.info(f"   Max daily trades: {limits.max_daily_trades}")
                self.logger.info(f"   Max open positions: {limits.max_open_positions}")
                self.logger.info(f"   Max daily loss: ${limits.max_daily_loss}")
                self.logger.info(f"   Max drawdown: {limits.max_drawdown_percent}%")
                
            except Exception as e:
                self.logger.error(f"❌ Failed to initialize account limits for {account_config.get('account_id', 'unknown')}: {e}")
    
    async def assess_trading_risk(
        self,
        account_id: str,
        account_info: AccountInfo,
        recent_trades: List[Dict[str, Any]],
        mt5_client = None
    ) -> RiskAssessment:
        """
        Comprehensive risk assessment - replaces all duplicate risk checking
        """
        async with self._lock:
            try:
                # Get account limits
                limits = self.account_limits.get(account_id)
                if not limits:
                    self.logger.warning(f"⚠️ No risk limits found for account {account_id}, using defaults")
                    limits = AccountLimits(
                        max_daily_trades=10,
                        max_open_positions=5,
                        max_pending_orders=10,
                        max_daily_loss=5.0,
                        max_drawdown_percent=15.0
                    )
                
                # Get or create daily stats
                today = datetime.now().date().isoformat()
                daily_key = f"{account_id}_{today}"
                daily_stats = self.daily_stats.get(daily_key)
                
                if not daily_stats:
                    daily_stats = await self._calculate_daily_stats(account_id, recent_trades, mt5_client)
                    self.daily_stats[daily_key] = daily_stats
                else:
                    # Update existing stats
                    daily_stats = await self._update_daily_stats(daily_stats, recent_trades, mt5_client)
                    self.daily_stats[daily_key] = daily_stats
                
                # Perform comprehensive risk assessment
                assessment = await self._perform_risk_assessment(account_id, limits, daily_stats, account_info)
                
                # Cache the assessment
                self.risk_assessments[account_id] = assessment
                
                # Log assessment if not LOW risk
                if assessment.risk_level != RiskLevel.LOW:
                    self.logger.warning(f"🚨 Risk Assessment - Account {account_id}: {assessment.risk_level.value}")
                    for reason in assessment.reasons:
                        self.logger.warning(f"   • {reason}")
                
                return assessment
                
            except Exception as e:
                self.logger.error(f"❌ Risk assessment failed for account {account_id}: {e}")
                # Return safe default assessment
                return RiskAssessment(
                    account_id=account_id,
                    risk_level=RiskLevel.CRITICAL,
                    allow_trading=False,
                    allow_new_positions=False,
                    recommended_volume_multiplier=0.0,
                    reasons=[f"Risk assessment failed: {e}"]
                )
    
    async def _calculate_daily_stats(
        self,
        account_id: str,
        recent_trades: List[Dict[str, Any]],
        mt5_client = None
    ) -> DailyStats:
        """Calculate daily statistics from trade history and current positions"""
        try:
            today = datetime.now().date()
            today_str = today.isoformat()
            
            # Calculate realized P&L from closed trades
            realized_pnl = 0.0
            trades_count = 0
            consecutive_losses = 0
            
            # Sort trades by time (most recent first)
            sorted_trades = sorted(recent_trades, key=lambda x: x.get('time', 0), reverse=True)
            
            for trade in sorted_trades:
                # Check if trade is from today
                trade_time = trade.get('time', 0)
                if isinstance(trade_time, datetime):
                    trade_date = trade_time.date()
                elif isinstance(trade_time, (int, float)) and trade_time > 0:
                    trade_date = datetime.fromtimestamp(trade_time).date()
                else:
                    continue
                
                if trade_date == today:
                    profit = trade.get('profit', 0.0)
                    realized_pnl += profit
                    trades_count += 1
                    
                    # Count consecutive losses (from most recent)
                    if profit < 0:
                        consecutive_losses += 1
                    else:
                        break  # Stop counting when we hit a win
            
            # Calculate unrealized P&L from open positions
            unrealized_pnl = 0.0
            if mt5_client:
                try:
                    positions = mt5_client.get_positions() or []
                    for position in positions:
                        unrealized_pnl += position.get('profit', 0.0)
                except Exception as e:
                    self.logger.warning(f"⚠️ Could not get positions for unrealized P&L: {e}")
            
            total_pnl = realized_pnl + unrealized_pnl
            
            return DailyStats(
                account_id=account_id,
                date=today_str,
                trades_count=trades_count,
                realized_pnl=realized_pnl,
                unrealized_pnl=unrealized_pnl,
                total_pnl=total_pnl,
                consecutive_losses=consecutive_losses,
                last_update=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"❌ Error calculating daily stats for {account_id}: {e}")
            # Return safe default
            return DailyStats(
                account_id=account_id,
                date=datetime.now().date().isoformat(),
                trades_count=0,
                realized_pnl=0.0,
                unrealized_pnl=0.0,
                total_pnl=0.0,
                consecutive_losses=0,
                last_update=datetime.now()
            )
    
    async def _update_daily_stats(
        self,
        current_stats: DailyStats,
        recent_trades: List[Dict[str, Any]],
        mt5_client = None
    ) -> DailyStats:
        """Update existing daily stats with latest data"""
        # For now, recalculate - could be optimized to only update deltas
        return await self._calculate_daily_stats(current_stats.account_id, recent_trades, mt5_client)

    async def _perform_risk_assessment(
        self,
        account_id: str,
        limits: AccountLimits,
        daily_stats: DailyStats,
        account_info: AccountInfo
    ) -> RiskAssessment:
        """Perform comprehensive risk assessment"""
        try:
            reasons = []
            risk_level = RiskLevel.LOW
            allow_trading = True
            allow_new_positions = True
            volume_multiplier = 1.0
            cooling_off_until = None

            # 1. Check daily trade limits
            if daily_stats.trades_count >= limits.max_daily_trades:
                risk_level = RiskLevel.CRITICAL
                allow_trading = False
                allow_new_positions = False
                reasons.append(f"Daily trade limit reached: {daily_stats.trades_count}/{limits.max_daily_trades}")
            elif daily_stats.trades_count >= limits.max_daily_trades * 0.8:
                risk_level = max(risk_level, RiskLevel.HIGH)
                volume_multiplier = min(volume_multiplier, 0.5)
                reasons.append(f"Approaching daily trade limit: {daily_stats.trades_count}/{limits.max_daily_trades}")

            # 2. Check daily loss limits
            daily_loss = abs(min(0, daily_stats.total_pnl))  # Only count losses

            # Use percentage-based limit if available, otherwise absolute
            if limits.max_daily_loss_percent:
                max_loss_amount = account_info.balance * (limits.max_daily_loss_percent / 100)
            else:
                max_loss_amount = limits.max_daily_loss

            if daily_loss >= max_loss_amount:
                risk_level = RiskLevel.CRITICAL
                allow_trading = False
                allow_new_positions = False
                cooling_off_until = datetime.now().replace(hour=23, minute=59, second=59)
                reasons.append(f"Daily loss limit exceeded: ${daily_loss:.2f}/${max_loss_amount:.2f}")
            elif daily_loss >= max_loss_amount * 0.8:
                risk_level = max(risk_level, RiskLevel.HIGH)
                volume_multiplier = min(volume_multiplier, 0.3)
                reasons.append(f"Approaching daily loss limit: ${daily_loss:.2f}/${max_loss_amount:.2f}")

            # 3. Check consecutive losses
            if daily_stats.consecutive_losses >= limits.max_consecutive_losses:
                risk_level = RiskLevel.CRITICAL
                allow_trading = False
                cooling_off_until = datetime.now() + timedelta(hours=2)
                reasons.append(f"Critical loss streak: {daily_stats.consecutive_losses} consecutive losses")
            elif daily_stats.consecutive_losses >= 3:
                risk_level = max(risk_level, RiskLevel.HIGH)
                volume_multiplier = min(volume_multiplier, 0.5)
                reasons.append(f"Loss streak detected: {daily_stats.consecutive_losses} consecutive losses")

            # 4. Check drawdown
            if account_info.equity > 0:
                current_drawdown = ((account_info.balance - account_info.equity) / account_info.balance) * 100

                if current_drawdown >= limits.max_drawdown_percent:
                    risk_level = RiskLevel.CRITICAL
                    allow_trading = False
                    reasons.append(f"Maximum drawdown exceeded: {current_drawdown:.1f}%/{limits.max_drawdown_percent:.1f}%")
                elif current_drawdown >= limits.max_drawdown_percent * 0.7:
                    risk_level = max(risk_level, RiskLevel.HIGH)
                    volume_multiplier = min(volume_multiplier, 0.4)
                    reasons.append(f"High drawdown: {current_drawdown:.1f}%/{limits.max_drawdown_percent:.1f}%")

            # 5. Check cooling off period
            if account_id in self.cooling_off_accounts:
                cooling_off_end = self.cooling_off_accounts[account_id]
                if datetime.now() < cooling_off_end:
                    risk_level = RiskLevel.CRITICAL
                    allow_trading = False
                    allow_new_positions = False
                    cooling_off_until = cooling_off_end
                    reasons.append(f"Account in cooling off period until {cooling_off_end.strftime('%H:%M:%S')}")
                else:
                    # Cooling off period ended
                    del self.cooling_off_accounts[account_id]

            # Set cooling off if determined
            if cooling_off_until:
                self.cooling_off_accounts[account_id] = cooling_off_until

            return RiskAssessment(
                account_id=account_id,
                risk_level=risk_level,
                allow_trading=allow_trading,
                allow_new_positions=allow_new_positions,
                recommended_volume_multiplier=volume_multiplier,
                reasons=reasons,
                cooling_off_until=cooling_off_until,
                daily_stats=daily_stats
            )

        except Exception as e:
            self.logger.error(f"❌ Risk assessment calculation failed for {account_id}: {e}")
            return RiskAssessment(
                account_id=account_id,
                risk_level=RiskLevel.CRITICAL,
                allow_trading=False,
                allow_new_positions=False,
                recommended_volume_multiplier=0.0,
                reasons=[f"Risk assessment error: {e}"]
            )

    async def can_place_new_trade(self, account_id: str) -> Tuple[bool, List[str]]:
        """Quick check if account can place new trades"""
        try:
            assessment = self.risk_assessments.get(account_id)
            if not assessment:
                return False, ["No risk assessment available - run assess_trading_risk first"]

            if not assessment.allow_trading:
                return False, assessment.reasons

            if not assessment.allow_new_positions:
                return False, assessment.reasons

            return True, []

        except Exception as e:
            self.logger.error(f"❌ Error checking trade permission for {account_id}: {e}")
            return False, [f"Error checking permissions: {e}"]

    async def get_recommended_volume_multiplier(self, account_id: str) -> float:
        """Get recommended volume multiplier for risk management"""
        try:
            assessment = self.risk_assessments.get(account_id)
            if not assessment:
                return 0.0  # No assessment = no trading

            return assessment.recommended_volume_multiplier

        except Exception as e:
            self.logger.error(f"❌ Error getting volume multiplier for {account_id}: {e}")
            return 0.0

    async def update_trade_executed(self, account_id: str, trade_result: Dict[str, Any]) -> None:
        """Update stats when a trade is executed"""
        async with self._lock:
            try:
                today = datetime.now().date().isoformat()
                daily_key = f"{account_id}_{today}"

                if daily_key in self.daily_stats:
                    stats = self.daily_stats[daily_key]
                    stats.trades_count += 1
                    stats.last_update = datetime.now()

                    self.logger.info(f"📊 Trade executed - Account {account_id}: {stats.trades_count} trades today")

            except Exception as e:
                self.logger.error(f"❌ Error updating trade stats for {account_id}: {e}")

    def get_daily_stats(self, account_id: str) -> Optional[DailyStats]:
        """Get current daily stats for an account"""
        today = datetime.now().date().isoformat()
        daily_key = f"{account_id}_{today}"
        return self.daily_stats.get(daily_key)

    def get_risk_assessment(self, account_id: str) -> Optional[RiskAssessment]:
        """Get current risk assessment for an account"""
        return self.risk_assessments.get(account_id)


# Global unified risk manager instance
unified_risk_manager = UnifiedRiskManager()
